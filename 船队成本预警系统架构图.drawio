<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-27T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="船队成本预警系统架构图" id="system-architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <mxCell id="title" value="船队成本预警系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="627" y="20" width="400" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="data-source-layer" value="数据源层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="80" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="ship-system" value="船舶运营系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="30" y="150" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="finance-system" value="财务系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="130" y="150" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="other-systems" value="其他业务系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="80" y="230" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-warehouse" value="数据仓库(ADS层)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="190" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-input-layer" value="数据接入层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="80" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="spring-integration" value="Spring Integration&#xa;数据采集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="460" y="150" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="flink-processing" value="Apache Flink&#xa;数据清洗转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="580" y="150" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="java-nio" value="Java NIO&#xa;高效数据读取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="520" y="230" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="rule-layer" value="规则处理层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="750" y="80" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="drools-engine" value="Drools规则引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="720" y="150" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="spring-el" value="Spring EL&#xa;动态规则调整" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="840" y="150" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="rule-templates" value="规则模板库&#xa;多维度预警配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="780" y="230" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="push-layer" value="推送层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1000" y="80" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="websocket" value="WebSocket&#xa;实时推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="970" y="150" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="event-driven" value="事件驱动模型&#xa;ApplicationEvent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="1070" y="150" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="multi-channel" value="多渠道推送&#xa;APP/邮件/短信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="1020" y="230" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="storage-layer" value="存储层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="350" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="mysql" value="MySQL&#xa;数据持久化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="450" y="420" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="redis" value="Redis&#xa;高频访问缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="570" y="420" width="100" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="monitor-layer" value="监控运维层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="750" y="350" width="120" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="failure-handling" value="失败处理&#xa;重试机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="700" y="420" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="monitoring" value="运行监控&#xa;可视化面板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="800" y="420" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="log-tracking" value="问题追溯&#xa;日志分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="900" y="420" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="microservice-frame" value="Spring Cloud 微服务框架" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#1ba1e2;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1200" y="200" width="200" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="spring-boot" value="Spring Boot&#xa;核心服务模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#36393d;" vertex="1" parent="1">
          <mxGeometry x="1220" y="300" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="service-discovery" value="服务发现&#xa;配置中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#36393d;" vertex="1" parent="1">
          <mxGeometry x="1320" y="300" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="edge1" edge="1" parent="1" source="ship-system" target="data-warehouse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" edge="1" parent="1" source="finance-system" target="data-warehouse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge3" edge="1" parent="1" source="other-systems" target="data-warehouse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge4" edge="1" parent="1" source="data-warehouse" target="spring-integration">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" edge="1" parent="1" source="spring-integration" target="flink-processing">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" edge="1" parent="1" source="flink-processing" target="drools-engine">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge7" edge="1" parent="1" source="drools-engine" target="websocket">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" edge="1" parent="1" source="drools-engine" target="mysql">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" edge="1" parent="1" source="drools-engine" target="redis">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge10" edge="1" parent="1" source="websocket" target="failure-handling">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow1" value="多源数据采集" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="220" y="160" width="70" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="flow2" value="实时数据处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="430" y="160" width="70" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="flow3" value="规则匹配预警" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="690" y="160" width="70" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="flow4" value="多渠道推送" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="950" y="160" width="70" height="20" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
