<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-27T00:00:00.000Z" agent="5.0" etag="xxx" version="24.7.17">
  <diagram name="船队成本预警系统架构图" id="system-architecture">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <mxCell id="title" value="船队成本预警系统架构图" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="300" y="10" width="400" height="30" as="geometry" />
        </mxCell>

        <mxCell id="data-source-layer" value="数据源层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="60" width="150" height="30" as="geometry" />
        </mxCell>

        <mxCell id="ship-system" value="船舶运营系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="30" y="100" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="finance-system" value="财务系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="110" y="100" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="other-systems" value="其他业务系统" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="70" y="150" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="data-warehouse" value="数据仓库&#xa;(ADS层)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="250" y="120" width="80" height="50" as="geometry" />
        </mxCell>

        <mxCell id="data-input-layer" value="数据接入层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="60" width="150" height="30" as="geometry" />
        </mxCell>

        <mxCell id="spring-integration" value="Spring Integration&#xa;数据采集" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="360" y="100" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="flink-processing" value="Apache Flink&#xa;数据清洗转换" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="450" y="100" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="java-nio" value="Java NIO&#xa;高效数据读取" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="405" y="150" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="rule-layer" value="规则处理层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="580" y="60" width="150" height="30" as="geometry" />
        </mxCell>

        <mxCell id="drools-engine" value="Drools规则引擎" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="560" y="100" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="spring-el" value="Spring EL&#xa;动态规则调整" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="650" y="100" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="rule-templates" value="规则模板库&#xa;多维度预警配置" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="605" y="150" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="push-layer" value="推送层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="780" y="60" width="150" height="30" as="geometry" />
        </mxCell>

        <mxCell id="websocket" value="WebSocket&#xa;实时推送" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="760" y="100" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="event-driven" value="事件驱动模型&#xa;ApplicationEvent" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="840" y="100" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="multi-channel" value="多渠道推送&#xa;APP/邮件/短信" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="800" y="150" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="storage-layer" value="存储层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="380" y="220" width="150" height="30" as="geometry" />
        </mxCell>

        <mxCell id="mysql" value="MySQL&#xa;数据持久化" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="360" y="260" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="redis" value="Redis&#xa;高频访问缓存" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="450" y="260" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="monitor-layer" value="监控运维层" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="580" y="220" width="150" height="30" as="geometry" />
        </mxCell>

        <mxCell id="failure-handling" value="失败处理&#xa;重试机制" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="560" y="260" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="monitoring" value="运行监控&#xa;可视化面板" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="640" y="260" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="log-tracking" value="问题追溯&#xa;日志分析" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="720" y="260" width="70" height="40" as="geometry" />
        </mxCell>

        <mxCell id="microservice-frame" value="Spring Cloud 微服务框架" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e6f3ff;strokeColor=#1ba1e2;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="320" width="880" height="40" as="geometry" />
        </mxCell>

        <mxCell id="spring-boot" value="Spring Boot&#xa;核心服务模块" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#36393d;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="200" y="370" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="service-discovery" value="服务发现&#xa;配置中心" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#36393d;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="300" y="370" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="gateway" value="API网关" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#36393d;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="400" y="370" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="config-center" value="配置管理" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#36393d;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="500" y="370" width="80" height="40" as="geometry" />
        </mxCell>

        <mxCell id="load-balancer" value="负载均衡" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#cce5ff;strokeColor=#36393d;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="600" y="370" width="80" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="edge1" edge="1" parent="1" source="ship-system" target="data-warehouse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge2" edge="1" parent="1" source="finance-system" target="data-warehouse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge3" edge="1" parent="1" source="other-systems" target="data-warehouse">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge4" edge="1" parent="1" source="data-warehouse" target="spring-integration">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge5" edge="1" parent="1" source="spring-integration" target="flink-processing">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge6" edge="1" parent="1" source="flink-processing" target="drools-engine">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge7" edge="1" parent="1" source="drools-engine" target="websocket">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge8" edge="1" parent="1" source="drools-engine" target="mysql">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge9" edge="1" parent="1" source="drools-engine" target="redis">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="edge10" edge="1" parent="1" source="websocket" target="failure-handling">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="flow1" value="数据采集" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="200" y="125" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="flow2" value="数据处理" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="340" y="125" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="flow3" value="规则匹配" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="540" y="125" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="flow4" value="预警推送" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="740" y="125" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="flow5" value="数据存储" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="430" y="200" width="50" height="15" as="geometry" />
        </mxCell>

        <mxCell id="flow6" value="监控运维" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;fontColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="630" y="200" width="50" height="15" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
